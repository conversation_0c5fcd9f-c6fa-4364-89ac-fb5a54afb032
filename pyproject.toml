[tool.ruff]

target-version = "py39"

[tool.ruff.lint]

extend-select = [
  "B",
  "C",
  "I",
  "W",
]

exclude = [
	"extensions",
	"extensions-disabled",
]

ignore = [
	"E501", # Line too long
	"E721", # Do not compare types, use `isinstance`
	"E731", # Do not assign a `lambda` expression, use a `def`
	
	"I001", # Import block is un-sorted or un-formatted
	"C901", # Function is too complex
	"C408", # Rewrite as a literal
	"W605", # invalid escape sequence, messes with some docstrings
]

[tool.ruff.lint.per-file-ignores]
"webui.py" = ["E402"]  # Module level import not at top of file

[tool.ruff.lint.flake8-bugbear]
# Allow default arguments like, e.g., `data: List[str] = fastapi.Query(None)`.
extend-immutable-calls = ["fastapi.Depends", "fastapi.security.HTTPBasic"]

[tool.pytest.ini_options]
base_url = "http://127.0.0.1:7860"
