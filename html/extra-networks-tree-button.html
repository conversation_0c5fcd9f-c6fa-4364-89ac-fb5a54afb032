<span data-filterable-item-text hidden>{search_terms}</span>
<div class="tree-list-content {subclass}"
    type="button"
    onclick="extraNetworksTreeOnClick(event, '{tabname}', '{extra_networks_tabname}');{onclick_extra}"
    data-path="{data_path}"
    data-hash="{data_hash}"
>
    <span class='tree-list-item-action tree-list-item-action--leading'>
        {action_list_item_action_leading}
    </span>
    <span class="tree-list-item-visual tree-list-item-visual--leading">
        {action_list_item_visual_leading}
    </span>
    <span class="tree-list-item-label tree-list-item-label--truncate">
        {action_list_item_label}
    </span>
    <span class="tree-list-item-visual tree-list-item-visual--trailing">
        {action_list_item_visual_trailing}
    </span>
    <span class="tree-list-item-action tree-list-item-action--trailing">
        {action_list_item_action_trailing}
    </span>
</div>